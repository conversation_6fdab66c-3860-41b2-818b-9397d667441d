schema;responses:
  type: "map"
  allowempty: True
  mapping:
    regex;(.+):
      type: "seq"
      required: False
      nullable: False
      func: require_response_keys
      sequence:
      - type: "map"
        required: True
        allowempty: False
        mapping:
          id:
            type: "str"
            required: False
          text:
            type: "str"
          image:
            type: "str"
          custom:
            type: "map"
            allowempty: True
          buttons:
            type: "seq"
            sequence:
            - type: "map"
              allowempty: True
              mapping:
                title:
                  type: "str"
                payload:
                  type: "str"
          button_type:
            type: "str"
          quick_replies:
            type: "seq"
            sequence:
            - type: "map"
              allowempty: True
              mapping:
                title:
                  type: "str"
                payload:
                  type: "str"
          attachment:
            type: "map"
            allowempty: True
          elements:
            type: "seq"
            sequence:
            - type: "map"
              allowempty: True
          channel:
            type: "str"
          metadata:
            type: "any"
          condition:
            type: "seq"
            sequence:
              - type: "map"
                allowempty: False
                mapping:
                  type:
                    type: "str"
                  name:
                    type: "str"
                  value:
                    type: "any"
