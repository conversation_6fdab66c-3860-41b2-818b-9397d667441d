# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from tensorflow.tsl.profiler.protobuf import profiler_analysis_pb2 as tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2


class ProfileAnalysisStub(object):
  """//////////////////////////////////////////////////////////////////////////////
  ProfileAnalysis service provide entry point for profiling TPU and for
  serving profiled data to TensorBoard through GRPC
  //////////////////////////////////////////////////////////////////////////////
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.NewSession = channel.unary_unary(
        '/tensorflow.ProfileAnalysis/NewSession',
        request_serializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.NewProfileSessionRequest.SerializeToString,
        response_deserializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.NewProfileSessionResponse.FromString,
        )
    self.EnumSessions = channel.unary_unary(
        '/tensorflow.ProfileAnalysis/EnumSessions',
        request_serializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.EnumProfileSessionsAndToolsRequest.SerializeToString,
        response_deserializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.EnumProfileSessionsAndToolsResponse.FromString,
        )
    self.GetSessionToolData = channel.unary_unary(
        '/tensorflow.ProfileAnalysis/GetSessionToolData',
        request_serializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.ProfileSessionDataRequest.SerializeToString,
        response_deserializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.ProfileSessionDataResponse.FromString,
        )


class ProfileAnalysisServicer(object):
  """//////////////////////////////////////////////////////////////////////////////
  ProfileAnalysis service provide entry point for profiling TPU and for
  serving profiled data to TensorBoard through GRPC
  //////////////////////////////////////////////////////////////////////////////
  """

  def NewSession(self, request, context):
    """Starts a profiling session, blocks until it completes.
    TPUProfileAnalysis service delegate this to TPUProfiler service.
    Populate the profiled data in repository, then return status to caller.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def EnumSessions(self, request, context):
    """Enumerate existing sessions and return available profile tools.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSessionToolData(self, request, context):
    """Retrieve specific tool's data for specific session.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ProfileAnalysisServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'NewSession': grpc.unary_unary_rpc_method_handler(
          servicer.NewSession,
          request_deserializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.NewProfileSessionRequest.FromString,
          response_serializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.NewProfileSessionResponse.SerializeToString,
      ),
      'EnumSessions': grpc.unary_unary_rpc_method_handler(
          servicer.EnumSessions,
          request_deserializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.EnumProfileSessionsAndToolsRequest.FromString,
          response_serializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.EnumProfileSessionsAndToolsResponse.SerializeToString,
      ),
      'GetSessionToolData': grpc.unary_unary_rpc_method_handler(
          servicer.GetSessionToolData,
          request_deserializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.ProfileSessionDataRequest.FromString,
          response_serializer=tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__analysis__pb2.ProfileSessionDataResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'tensorflow.ProfileAnalysis', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
