# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/tsl/profiler/protobuf/profiler_options.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7tensorflow/tsl/profiler/protobuf/profiler_options.proto\x12\ntensorflow\"\x83\x03\n\x0eProfileOptions\x12\x0f\n\x07version\x18\x05 \x01(\r\x12:\n\x0b\x64\x65vice_type\x18\x06 \x01(\x0e\x32%.tensorflow.ProfileOptions.DeviceType\x12\x1b\n\x13include_dataset_ops\x18\x01 \x01(\x08\x12\x19\n\x11host_tracer_level\x18\x02 \x01(\r\x12\x1b\n\x13\x64\x65vice_tracer_level\x18\x03 \x01(\r\x12\x1b\n\x13python_tracer_level\x18\x04 \x01(\r\x12\x18\n\x10\x65nable_hlo_proto\x18\x07 \x01(\x08\x12\x1a\n\x12start_timestamp_ns\x18\x08 \x01(\x04\x12\x13\n\x0b\x64uration_ms\x18\t \x01(\x04\x12\x17\n\x0frepository_path\x18\n \x01(\t\"N\n\nDeviceType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x07\n\x03\x43PU\x10\x01\x12\x07\n\x03GPU\x10\x02\x12\x07\n\x03TPU\x10\x03\x12\x14\n\x10PLUGGABLE_DEVICE\x10\x04\"\xd0\x01\n#RemoteProfilerSessionManagerOptions\x12\x34\n\x10profiler_options\x18\x01 \x01(\x0b\x32\x1a.tensorflow.ProfileOptions\x12\x19\n\x11service_addresses\x18\x02 \x03(\t\x12%\n\x1dsession_creation_timestamp_ns\x18\x03 \x01(\x04\x12\x1f\n\x17max_session_duration_ms\x18\x04 \x01(\x04\x12\x10\n\x08\x64\x65lay_ms\x18\x05 \x01(\x04\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.tsl.profiler.protobuf.profiler_options_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PROFILEOPTIONS._serialized_start=72
  _PROFILEOPTIONS._serialized_end=459
  _PROFILEOPTIONS_DEVICETYPE._serialized_start=381
  _PROFILEOPTIONS_DEVICETYPE._serialized_end=459
  _REMOTEPROFILERSESSIONMANAGEROPTIONS._serialized_start=462
  _REMOTEPROFILERSESSIONMANAGEROPTIONS._serialized_end=670
# @@protoc_insertion_point(module_scope)
