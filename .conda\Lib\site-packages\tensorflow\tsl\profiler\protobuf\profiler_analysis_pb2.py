# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/tsl/profiler/protobuf/profiler_analysis.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.tsl.profiler.protobuf import profiler_service_pb2 as tensorflow_dot_tsl_dot_profiler_dot_protobuf_dot_profiler__service__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8tensorflow/tsl/profiler/protobuf/profiler_analysis.proto\x12\ntensorflow\x1a\x37tensorflow/tsl/profiler/protobuf/profiler_service.proto\"\x83\x01\n\x18NewProfileSessionRequest\x12+\n\x07request\x18\x01 \x01(\x0b\x32\x1a.tensorflow.ProfileRequest\x12\x17\n\x0frepository_root\x18\x02 \x01(\t\x12\r\n\x05hosts\x18\x03 \x03(\t\x12\x12\n\nsession_id\x18\x04 \x01(\t\"G\n\x19NewProfileSessionResponse\x12\x15\n\rerror_message\x18\x01 \x01(\t\x12\x13\n\x0b\x65mpty_trace\x18\x02 \x01(\x08\"=\n\"EnumProfileSessionsAndToolsRequest\x12\x17\n\x0frepository_root\x18\x01 \x01(\t\"A\n\x12ProfileSessionInfo\x12\x12\n\nsession_id\x18\x01 \x01(\t\x12\x17\n\x0f\x61vailable_tools\x18\x02 \x03(\t\"n\n#EnumProfileSessionsAndToolsResponse\x12\x15\n\rerror_message\x18\x01 \x01(\t\x12\x30\n\x08sessions\x18\x02 \x03(\x0b\x32\x1e.tensorflow.ProfileSessionInfo\"\xec\x01\n\x19ProfileSessionDataRequest\x12\x17\n\x0frepository_root\x18\x01 \x01(\t\x12\x12\n\nsession_id\x18\x02 \x01(\t\x12\x11\n\thost_name\x18\x05 \x01(\t\x12\x11\n\ttool_name\x18\x03 \x01(\t\x12I\n\nparameters\x18\x04 \x03(\x0b\x32\x35.tensorflow.ProfileSessionDataRequest.ParametersEntry\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"Z\n\x1aProfileSessionDataResponse\x12\x15\n\rerror_message\x18\x01 \x01(\t\x12\x15\n\routput_format\x18\x02 \x01(\t\x12\x0e\n\x06output\x18\x03 \x01(\x0c\x32\xc8\x02\n\x0fProfileAnalysis\x12[\n\nNewSession\x12$.tensorflow.NewProfileSessionRequest\x1a%.tensorflow.NewProfileSessionResponse\"\x00\x12q\n\x0c\x45numSessions\x12..tensorflow.EnumProfileSessionsAndToolsRequest\x1a/.tensorflow.EnumProfileSessionsAndToolsResponse\"\x00\x12\x65\n\x12GetSessionToolData\x12%.tensorflow.ProfileSessionDataRequest\x1a&.tensorflow.ProfileSessionDataResponse\"\x00\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.tsl.profiler.protobuf.profiler_analysis_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _PROFILESESSIONDATAREQUEST_PARAMETERSENTRY._options = None
  _PROFILESESSIONDATAREQUEST_PARAMETERSENTRY._serialized_options = b'8\001'
  _NEWPROFILESESSIONREQUEST._serialized_start=130
  _NEWPROFILESESSIONREQUEST._serialized_end=261
  _NEWPROFILESESSIONRESPONSE._serialized_start=263
  _NEWPROFILESESSIONRESPONSE._serialized_end=334
  _ENUMPROFILESESSIONSANDTOOLSREQUEST._serialized_start=336
  _ENUMPROFILESESSIONSANDTOOLSREQUEST._serialized_end=397
  _PROFILESESSIONINFO._serialized_start=399
  _PROFILESESSIONINFO._serialized_end=464
  _ENUMPROFILESESSIONSANDTOOLSRESPONSE._serialized_start=466
  _ENUMPROFILESESSIONSANDTOOLSRESPONSE._serialized_end=576
  _PROFILESESSIONDATAREQUEST._serialized_start=579
  _PROFILESESSIONDATAREQUEST._serialized_end=815
  _PROFILESESSIONDATAREQUEST_PARAMETERSENTRY._serialized_start=766
  _PROFILESESSIONDATAREQUEST_PARAMETERSENTRY._serialized_end=815
  _PROFILESESSIONDATARESPONSE._serialized_start=817
  _PROFILESESSIONDATARESPONSE._serialized_end=907
  _PROFILEANALYSIS._serialized_start=910
  _PROFILEANALYSIS._serialized_end=1238
# @@protoc_insertion_point(module_scope)
