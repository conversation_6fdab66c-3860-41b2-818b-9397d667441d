# Copyright 2020 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Utilities for exporting symbols."""

from tensorflow.python.util import tf_export


def public_name(np_fun_name):
  return "experimental.numpy." + np_fun_name


def np_export(np_fun_name):
  return tf_export.tf_export(public_name(np_fun_name), v1=[])


def np_export_constant(module_name, name, value):
  np_export(name).export_constant(module_name, name)
  return value
