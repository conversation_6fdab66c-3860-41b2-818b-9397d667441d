# Copyright 2015 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Types for specifying saving and loading behavior."""


class SaveSpec:
  """Class used to describe tensor slices that need to be saved."""

  def __init__(self, tensor, slice_spec, name, dtype=None, device=None):
    """Creates a `SaveSpec` object.

    Args:
      tensor: the tensor to save or callable that produces a tensor to save.
        If the value is `None`, the `SaveSpec` is ignored.
      slice_spec: the slice to be saved. See `Variable.SaveSliceInfo`.
      name: the name to save the tensor under.
      dtype: The data type of the Tensor. Required if `tensor` is callable.
        Used for error checking in the restore op.
      device: The device generating and consuming this tensor. Required if
        `tensor` is callable. Used to group objects to save by device.
    """
    self._tensor = tensor
    self.slice_spec = slice_spec
    self.name = name
    if callable(self._tensor):
      if dtype is None or device is None:
        raise AssertionError(
            "When passing a callable `tensor` to a SaveSpec, an explicit "
            "dtype and device must be provided.")
      self.dtype = dtype
      self.device = device
    else:
      self.dtype = tensor.dtype
      if device is not None:
        self.device = device
      else:
        self.device = tensor.device

  @property
  def tensor(self):
    return self._tensor() if callable(self._tensor) else self._tensor


class SaveableObject:
  """Base class for saving and restoring saveable objects."""

  def __init__(self, op, specs, name):
    """Creates a `SaveableObject` object.

    Args:
      op: the "producer" object that this class wraps; it produces a list of
        tensors to save.  E.g., a "Variable" object saving its backing tensor.
      specs: a list of SaveSpec, each element of which describes one tensor to
        save under this object. All Tensors must be on the same device.
      name: the name to save the object under.
    """
    self.op = op
    self.specs = specs
    self.name = name

  @property
  def device(self):
    """The device for SaveSpec Tensors."""
    return self.specs[0].device

  def restore(self, restored_tensors, restored_shapes):
    """Restores this object from 'restored_tensors'.

    Args:
      restored_tensors: the tensors that were loaded from a checkpoint
      restored_shapes: the shapes this object should conform to after
        restore, or None.

    Returns:
      An operation that restores the state of the object.

    Raises:
      ValueError: If the object cannot be restored using the provided
        parameters.
    """
    # pylint: disable=unused-argument
    raise ValueError("Calling an abstract method.")
