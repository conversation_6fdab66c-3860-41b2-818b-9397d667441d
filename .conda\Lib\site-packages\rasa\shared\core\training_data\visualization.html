<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rasa Core Visualisation</title>
    <script src="https://dagrejs.github.io/project/dagre-d3/latest/dagre-d3.min.js"></script>
    <script src="https://dagrejs.github.io/project/dagre/latest/dagre.min.js"></script>
    <script src="https://d3js.org/d3.v4.js"></script>
    <script src="https://dagrejs.github.io/project/graphlib-dot/v0.6.3/graphlib-dot.js"></script>
</head>
<body>
<div id="errormsg" style="color: #b00"></div>
<svg>
    <style id="graph-style">
        .node.invisible > rect {
            display: none;
        }

        .node.start > rect {
            fill: #7f7;
            rx: 30;
            ry: 18;
        }

        .node.end > rect {
            fill: #f77;
            rx: 30;
            ry: 18;
        }

        .node:not(.active) > rect, .node:not(.active) > .label {
            opacity: 0.4;
        }

        .edgePath:not(.active) path {
            opacity: 0.4;
        }

        .node.ellipsis > rect {
            fill: #CCC;
        }

        .node.intent > rect {
            fill: #7ff;
        }

        .node.dashed > rect {
            stroke-dasharray: 5;
        }

        text {
            font-weight: 300;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serf, serif;
            font-size: 14px;
            color: #1f1d1d;
        }

        .node rect {
            stroke: #444;
            fill: #fff;
            stroke-width: 1.5px;
        }

        .edgePath path {
            stroke: #333;
            stroke-width: 1.5px;
        }

        svg {
            position: fixed;
            top: 10px;
            left: 0;
            height: 100%;
            width: 100%
        }
    </style>
    <g></g>
</svg>
<script>

  function serveGraph() {
    let oldInputGraphValue;

    const url = 'visualization.dot';
    const refreshInterval = 500;

    // trigger a refresh by fetching an updated graph
    setInterval(function () {
      fetch(url).then(r => r.text()).then(dot => {
        document.getElementById('errormsg').innerHTML = '';
        if (oldInputGraphValue === dot) return;

        oldInputGraphValue = dot;
        drawGraph(dot);
      }).catch(err => {
        document.getElementById('errormsg').innerHTML =
          'Failed to update plot. (' + err.message + ')';
      });
    }, refreshInterval);
  }

  function drawGraph(graph) {
    let g = graphlibDot.read(graph);
    // Set margins, if not present
    if (!g.graph().hasOwnProperty("marginx") &&
      !g.graph().hasOwnProperty("marginy")) {
      g.graph().marginx = 20;
      g.graph().marginy = 20;
    }
    g.graph().transition = function (selection) {
      return selection.transition().duration(300);
    };
    // Render the graph into svg g
    d3.select("svg g").call(render, g);
  }
  // Set up zoom support
  const svg = d3.select("svg"),
    inner = d3.select("svg g"),
    zoom = d3.zoom().on("zoom", function () {
      inner.attr("transform", d3.event.transform);
    });
  svg.call(zoom);

  // Create and configure the renderer
  const render = dagreD3.render();

  let isClient = false;
  // { is-client };

  if (isClient) {
    // Mark all nodes and their edges as active
    cssRules = document.getElementById('graph-style').sheet.cssRules;
    cssRules[3].style.opacity = 1;
    cssRules[4].style.opacity = 1;

    let graph;
    // { graph-content };
    drawGraph(graph);
  } else {
    serveGraph();
  }


</script>
</body>
</html>
