# Copyright 2016 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

"""Ensure compatibility with future tensorflow versions.

   This ensures that your code will be minimally impacted by future tensorflow
   API changes. Import the module to prevent accidental usage of stale APIs.
"""
import tensorflow as tf


delattr(tf, 'arg_max')
delattr(tf, 'arg_min')
delattr(tf, 'create_partitioned_variables')
delattr(tf, 'deserialize_many_sparse')
delattr(tf, 'lin_space')
delattr(tf, 'parse_single_sequence_example')
delattr(tf, 'serialize_many_sparse')
delattr(tf, 'serialize_sparse')
delattr(tf, 'sparse_matmul')  # Use tf.matmul instead.
